/**
 * 动态微前端子应用配置
 */
import { prefetchApps, loadMicroApp } from 'qiankun'

// 调试信息：检查 qiankun 导入
console.log('qiankun 导入状态:', {
    prefetchApps: typeof prefetchApps,
    loadMicroApp: typeof loadMicroApp,
    hasQiankun: typeof window.qiankun !== 'undefined'
})

// 如果 prefetchApps 不可用
const safePrefetchApps = (apps) => {
    try {
        if (typeof prefetchApps === 'function') {
            const result = prefetchApps(apps)
            // 确保返回值是 Promise
            if (result && typeof result.then === 'function') {
                return result
            } else {
                console.warn('prefetchApps 未返回 Promise，返回兜底 Promise')
                return Promise.resolve()
            }
        } else {
            console.warn('prefetchApps 不可用，跳过预加载')
            return Promise.resolve()
        }
    } catch (error) {
        console.error('调用 prefetchApps 失败:', error)
        return Promise.resolve()
    }
}

// 动态生成的微应用配置缓存
let dynamicMicroApps = new Map()

// 动态路由映射缓存
let dynamicRouteToAppMap = {}

// 预加载队列
let preloadQueue = new Set()

// 预加载状态
let preloadStatus = new Map() // { appName: 'pending' | 'loading' | 'loaded' | 'error' }

// 预加载配置
const PRELOAD_CONFIG = {
    // 最大并发预加载数量
    maxConcurrent: 2,
    // 预加载延迟时间（ms）
    delay: 2000,
    // 空闲时间检测间隔（ms）
    idleInterval: 3000,
    // 是否启用预加载
    enabled: true
}

// 当前预加载数量
let currentPreloadCount = 0

// qiankun 预加载的应用缓存
let qiankunPreloadedApps = new Set()

/**
 * 诊断 qiankun 状态
 */
export function diagnoseQiankun() {
    const diagnosis = {
        prefetchAppsType: typeof prefetchApps,
        loadMicroAppType: typeof loadMicroApp,
        hasWindowQiankun: typeof window.qiankun !== 'undefined',
        nodeEnv: process.env.NODE_ENV,
        timestamp: new Date().toISOString()
    }

    console.log('=== qiankun 诊断信息 ===', diagnosis)

    // 测试 prefetchApps 调用
    if (typeof prefetchApps === 'function') {
        try {
            const testResult = prefetchApps([])
            console.log('prefetchApps([]) 返回值类型:', typeof testResult)
            console.log(
                'prefetchApps([]) 是否为 Promise:',
                testResult && typeof testResult.then === 'function'
            )
        } catch (error) {
            console.error('prefetchApps([]) 调用失败:', error)
        }
    }

    return diagnosis
}
// 路径前缀与子应用的映射关系
const PATH_TO_APP_MAPPING = {
    '/observation-center': {
        name: 'observation-center',
        entry: process.env.VUE_APP_OBSERVATION_CENTER_ENTRY || '//localhost:8082/observation-center',
        container: '#micro-app-container',
        activeRule: '/observation-center'
    }
    // 未来可以轻松添加新的子应用
    // '/data-center': {
    //     name: 'data-center',
    //     entry: process.env.VUE_APP_DATA_CENTER_ENTRY || '//localhost:8083/data-center',
    //     container: '#micro-app-container',
    //     activeRule: '/data-center'
    // }
}

/**
 * 根据路径前缀获取微应用配置
 */
function getMicroAppConfigByPath(pathname) {
    // 清理路径（去掉查询参数和hash）
    const cleanPath = pathname.split('?')[0].split('#')[0]

    // 精确匹配优先
    if (PATH_TO_APP_MAPPING[cleanPath]) {
        return PATH_TO_APP_MAPPING[cleanPath]
    }

    // 前缀匹配，选择最长匹配
    let bestMatch = null
    let maxLength = 0

    for (const [pathPrefix, config] of Object.entries(PATH_TO_APP_MAPPING)) {
        if (cleanPath.startsWith(pathPrefix + '/') || cleanPath === pathPrefix) {
            if (pathPrefix.length > maxLength) {
                bestMatch = config
                maxLength = pathPrefix.length
            }
        }
    }

    return bestMatch
}

/**
 * 获取所有已配置的微应用
 */
function getAllConfiguredMicroApps() {
    return Object.values(PATH_TO_APP_MAPPING)
}

/**
 * 使用 qiankun 预加载微应用
 */
async function preloadMicroApp(appConfig) {
    const { name, entry } = appConfig

    if (!PRELOAD_CONFIG.enabled) {
        console.log(`预加载已禁用，跳过应用: ${name}`)
        return false
    }

    if (preloadStatus.get(name) === 'loaded' || preloadStatus.get(name) === 'loading') {
        console.log(`应用 ${name} 已预加载或正在预加载`)
        return true
    }

    try {
        preloadStatus.set(name, 'loading')
        currentPreloadCount++

        console.log(`开始预加载微应用: ${name} (${entry})`)

        // 使用安全的 qiankun 预加载
        const prefetchPromise = safePrefetchApps([
            {
                name,
                entry
            }
        ])

        // 确保返回的是 Promise
        if (prefetchPromise && typeof prefetchPromise.then === 'function') {
            await prefetchPromise
        } else {
            console.warn(`应用 ${name} 预加载未返回有效 Promise，跳过`)
        }

        qiankunPreloadedApps.add(name)
        preloadStatus.set(name, 'loaded')
        console.log(`微应用 ${name} 预加载完成`)

        return true
    } catch (error) {
        preloadStatus.set(name, 'error')
        console.error(`微应用 ${name} 预加载失败:`, error)
        return false
    } finally {
        currentPreloadCount--
        // 继续处理预加载队列
        processPreloadQueue()
    }
}

/**
 * 添加应用到预加载队列
 */
export function addToPreloadQueue(appConfig) {
    if (!appConfig || !PRELOAD_CONFIG.enabled) return

    const { name } = appConfig

    // 避免重复添加
    if (
        preloadStatus.has(name) ||
        Array.from(preloadQueue).some((config) => config.name === name)
    ) {
        return
    }

    preloadQueue.add(appConfig)
    console.log(`应用 ${name} 已添加到预加载队列`)

    // 立即尝试处理队列
    setTimeout(() => processPreloadQueue(), PRELOAD_CONFIG.delay)
}

/**
 * 处理预加载队列
 */
function processPreloadQueue() {
    if (currentPreloadCount >= PRELOAD_CONFIG.maxConcurrent || preloadQueue.size === 0) {
        return
    }

    // 取出队列中的第一个应用
    const appConfig = preloadQueue.values().next().value
    if (appConfig) {
        preloadQueue.delete(appConfig)
        preloadMicroApp(appConfig)
    }
}

/**
 * 批量预加载多个应用
 */
export function batchPreload(appConfigs = []) {
    if (!PRELOAD_CONFIG.enabled || !Array.isArray(appConfigs)) return

    console.log(`批量预加载 ${appConfigs.length} 个应用`)

    if (appConfigs.length > 0) {
        // 使用 qiankun 批量预加载
        const qiankunApps = appConfigs.map((config) => ({
            name: config.name,
            entry: config.entry
        }))

        try {
            const prefetchPromise = safePrefetchApps(qiankunApps)

            // 双重检查确保有有效的 Promise
            if (prefetchPromise && typeof prefetchPromise.then === 'function') {
                prefetchPromise
                    .then(() => {
                        appConfigs.forEach((config) => {
                            preloadStatus.set(config.name, 'loaded')
                            qiankunPreloadedApps.add(config.name)
                        })
                        console.log(`批量预加载完成`)
                    })
                    .catch((error) => {
                        console.error('批量预加载失败:', error)
                        // 降级到单个预加载
                        appConfigs.forEach((appConfig) => {
                            addToPreloadQueue(appConfig)
                        })
                    })
            } else {
                console.warn('safePrefetchApps 未返回有效 Promise，降级到单个预加载')
                // 降级到单个预加载
                appConfigs.forEach((appConfig) => {
                    addToPreloadQueue(appConfig)
                })
            }
        } catch (error) {
            console.error('批量预加载过程中出错:', error)
            // 降级到单个预加载
            appConfigs.forEach((appConfig) => {
                addToPreloadQueue(appConfig)
            })
        }
    }
}

/**
 * 空闲时自动预加载
 */
export function enableIdlePreload() {
    if (!PRELOAD_CONFIG.enabled) return

    // 使用 requestIdleCallback 或 setTimeout 在空闲时预加载
    const idlePreload = () => {
        if (preloadQueue.size > 0 && currentPreloadCount < PRELOAD_CONFIG.maxConcurrent) {
            processPreloadQueue()
        }

        // 继续监听空闲时间
        if ('requestIdleCallback' in window) {
            requestIdleCallback(idlePreload, { timeout: PRELOAD_CONFIG.idleInterval })
        } else {
            setTimeout(idlePreload, PRELOAD_CONFIG.idleInterval)
        }
    }

    // 启动空闲预加载
    if ('requestIdleCallback' in window) {
        requestIdleCallback(idlePreload)
    } else {
        setTimeout(idlePreload, PRELOAD_CONFIG.idleInterval)
    }

    console.log('空闲时预加载已启用')
}

/**
 * 获取预加载状态
 */
export function getPreloadStatus(appName) {
    if (appName) {
        return preloadStatus.get(appName) || 'pending'
    }
    return Object.fromEntries(preloadStatus)
}

/**
 * 配置预加载选项
 */
export function configurePreload(options = {}) {
    Object.assign(PRELOAD_CONFIG, options)
    console.log('预加载配置已更新:', PRELOAD_CONFIG)
}

/**
 * 清空预加载队列和状态
 */
export function clearPreloadCache() {
    preloadQueue.clear()
    preloadStatus.clear()
    qiankunPreloadedApps.clear()
    currentPreloadCount = 0
    console.log('预加载缓存已清空')
}

/**
 * 检查应用是否已通过 qiankun 预加载
 */
export function isQiankunPreloaded(appName) {
    return qiankunPreloadedApps.has(appName)
}

/**
 * 注册动态微应用（保持兼容性，但现在基于路径）
 */
export function registerDynamicMicroApp(menuKey, menuName, menuUrl) {
    if (!menuKey || menuKey === 'system') return null

    // 尝试根据menuUrl找到对应的微应用配置
    const appConfig = getMicroAppConfigByPath(menuUrl)
    if (!appConfig) {
        console.warn(`未找到路径 ${menuUrl} 对应的微应用配置`)
        return null
    }

    // 建立路由映射
    setDynamicRouteMapping(menuUrl, appConfig.name)

    console.log(`注册微应用: ${menuKey} -> ${appConfig.name}, 路由: ${menuUrl}`)

    // 自动添加到预加载队列
    addToPreloadQueue(appConfig)

    return appConfig
}

/**
 * 根据menuKey获取微应用配置
 */
export function getMicroAppByMenuKey(menuKey) {
    return dynamicMicroApps.get(menuKey)
}

/**
 * 设置动态路由映射
 */
export function setDynamicRouteMapping(menuUrl, appName) {
    dynamicRouteToAppMap[menuUrl] = appName
}

/**
 * 根据路由路径获取子应用配置
 */
export function getMicroAppByRoute(route) {
    // 直接使用新的路径匹配逻辑
    return getMicroAppConfigByPath(route)
}

/**
 * 清空所有动态配置
 */
export function clearDynamicConfigs() {
    dynamicMicroApps.clear()
    dynamicRouteToAppMap = {}
}

/**
 * 获取所有注册的微应用（用于调试）
 */
export function getAllDynamicMicroApps() {
    // 返回所有已配置的微应用
    return getAllConfiguredMicroApps()
}

/**
 * 获取当前动态路由映射（用于调试）
 */
export function getDynamicRouteMapping() {
    return { ...dynamicRouteToAppMap }
}
