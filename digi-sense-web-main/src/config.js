// 应用具体配置
const APP_CONFIG = {
    sysName: 'gns-portal',
    // session有效时间 ms
    sessionDuration: 30 * 60 * 1000,
    routeMode: 'history',
    // 首页路由名称, 用于处理遭遇各种异常路由时的最终跳转路由
    indexPageName: 'Home',
    indexPath: '/home/<USER>/index',
    // publicRouter: ['Login', 'err403', 'err404', 'err500'], // 不需要鉴权的路由名称
    publicRouter: ['Login', 'EagleCrmEmbed', 'err403', 'err404', 'err500', 'embed500'], // embed是外系统跳转的中转页面
    setting: {
        fixedHeader: true,
        needTagsView: true,
        apiheaders: {
            appid: process.env.VUE_APP_ID ? process.env.VUE_APP_ID : ''
        }
    },
    topLevel: 'admin',
    useMock: false, // 是否使用mock数据
    formSize: 'small', // element-ui form 的大小 medium / small / mini
    apiPath: process.env.VUE_APP_apipath ? process.env.VUE_APP_apipath : '', // 接口服务器路径
    authPath: process.env.VUE_APP_authpath ? process.env.VUE_APP_authpath : '', // 统一认证地址
    accessToken: 'token',
    model: 0, // 0 表示常规系统模式 1 表示内嵌模式
    embedToken: 'EmbedToken', // 存储于本地的外部token缓存键名
    menuPermissions: true // 是否进行菜单鉴权，本地调试的时候可以设置为false，看到所有菜单 ++仅当本地开发调试时为false
}

module.exports = APP_CONFIG
