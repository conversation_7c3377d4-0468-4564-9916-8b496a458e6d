import baseRouter from '@/router/base-router'
import permissionRouter from '@/router/permission-router'
import config from '@/config'
import {
    registerDynamicMicroApp,
    clearDynamicConfigs,
    getAllDynamicMicroApps,
    getDynamicRouteMapping,
    getMicroAppConfigByPath
} from '@/micro-apps'
// function hasPermission (roles, route) {
//     if (route.meta && route.meta.roles) {
//         return roles.some((role) => route.meta.roles.includes(role))
//     } else {
//         return true
//     }
// }
function hasPermission(authInfo, route) {
    const { authBtns, authMenus } = authInfo
    if (!config.menuPermissions) return true
    if (route.meta && route.meta.code) {
        return authMenus.indexOf(route.meta.code) >= 0
    } else if (route.meta && route.meta.linkPrivId) {
        return authBtns.indexOf(String(route.meta.linkPrivId)) >= 0
    } else {
        return true
    }
}

// 根据字符串加载
export function importComponent(file) {
    return () => import(`@/${file}.vue`)
}

export function filterRoutes(routes, authInfo, parentPath = '') {
    const router = [] // 路由
    const menu = [] // 菜单
    routes.forEach((route) => {
        const tmp = { ...route }
        const tmpMenu = { ...route }
        if (hasPermission(authInfo, tmp)) {
            if (tmp.children) {
                let ppath = parentPath
                if (tmp.path) {
                    // 当前路由有path，有时路由设置 '' 作为默认路由
                    ppath = `${parentPath}/${tmp.path}` // 默认相对路径，和父路径拼接
                    if (tmp.path[0] === '/') {
                        // 如果有/绝对路径直接赋值
                        ppath = tmp.path
                    }
                }
                const filterObj = filterRoutes(tmp.children, authInfo, ppath)
                tmp.children = filterObj.router
                tmpMenu.children = filterObj.menu
                if (tmp.children.length) {
                    const childPath = tmp.children[0].path
                    if (childPath && Object.prototype.hasOwnProperty.call(tmp, 'redirect')) {
                        // 当前第一个子路由有path，设置重定向
                        if (childPath[0] === '/') {
                            tmp.redirect = childPath
                        } else {
                            tmp.redirect = `${ppath}/${childPath}`
                        }
                    }
                }
            }
            if (typeof tmp.component === 'string') {
                // 扫描，如果是字符串，通过拼接找到对应的组件
                tmp.component = importComponent(tmp.component)
            }
            if (tmp.meta) {
                if (tmp.meta.type === 'menu') {
                    if (tmp.children && tmp.children.length) {
                        router.push(...tmp.children)
                    }
                } else {
                    router.push(tmp)
                }
            } else {
                router.push(tmp)
            }
            menu.push(tmpMenu)
        }
    })
    return {
        router,
        menu
    }
}

export function filterPageRoutes(routes) {
    let res = {}
    routes.forEach((route) => {
        const tmp = { ...route }
        if (tmp.meta && tmp.meta.hasPageChild) {
            res[tmp.name] = tmp
        }
        if (tmp.children) {
            const child = filterPageRoutes(tmp.children)
            res = Object.assign({}, res, child)
        }
    })
    return res
}
const addMenuParentPath = (menus = [], parentpath = '') =>
    menus.map((menu) => {
        if (menu.children) {
            return {
                ...menu,
                children: addMenuParentPath(menu.children, menu.menuUrl)
            }
        }
        if (parentpath === '/') {
            return menu
        }
        return {
            ...menu,
            menuUrl: `${parentpath}${menu.menuUrl}`
        }
    })

const state = {
    routes: baseRouter,
    pageMenu: [],
    permissionRoutes: [],
    pageRoute: {}
}

// 生成微前端子应用路由
function generateMicroAppRoutes(menus) {
    const microAppRoutes = []

    // 清空之前的动态配置
    clearDynamicConfigs()

    // 递归遍历菜单，查找子应用菜单
    function traverseMenus(menuList) {
        menuList.forEach((menu) => {
            const { menuKey, menuUrl, menuName, children } = menu
            // 检查是否是子应用菜单 (menuKey !== 'system' 表示非主应用)
            if (menuKey && menuKey !== 'system' && menuUrl) {
                // 检查menuUrl是否匹配已配置的微应用路径
                const microAppConfig = getMicroAppConfigByPath(menuUrl)

                if (microAppConfig) {
                    // 动态注册微应用（保持兼容性）
                    const microApp = registerDynamicMicroApp(menuKey, menuName, menuUrl)

                    if (microApp) {
                        // 收集所有子菜单路由
                        const childRoutes = []

                        // 添加默认的微前端容器路由（主路由）
                        childRoutes.push({
                            path: '',
                            name: microApp.name,
                            component: () => import('@/components/MicroAppContainer.vue'),
                            meta: {
                                title: menuName || microApp.name,
                                breadcrumb: false,
                                microApp: true,
                                appName: microApp.name,
                                menuKey: menuKey,
                                code: menu.menuId
                            }
                        })

                    // 为子菜单生成路由
                    if (children && children.length > 0) {
                        children.forEach((childMenu) => {
                            if (childMenu.menuUrl) {
                                // 判断是否为当前微应用的子菜单
                                const isChildMicroApp =
                                    !childMenu.menuKey || childMenu.menuKey === menuKey

                                if (isChildMicroApp) {
                                    // 生成子路由路径：去掉开头的 /
                                    const childPath = childMenu.menuUrl.startsWith('/')
                                        ? childMenu.menuUrl.substring(1)
                                        : childMenu.menuUrl

                                    if (childPath) {
                                        console.log(
                                            `生成子路由: ${menuUrl}/${childPath} -> ${childMenu.menuName}`
                                        )

                                        childRoutes.push({
                                            path: childPath,
                                            name: `${microApp.name}_${childMenu.menuId}`,
                                            component: () =>
                                                import('@/components/MicroAppContainer.vue'),
                                            meta: {
                                                title: childMenu.menuName,
                                                breadcrumb: false,
                                                microApp: true,
                                                appName: microApp.name,
                                                menuKey: menuKey,
                                                code: childMenu.menuId,
                                                // 完整的菜单路径用于左侧菜单高亮
                                                activeSlideMenu: `${menuUrl}${childMenu.menuUrl}`
                                            }
                                        })
                                    }
                                }
                            }
                        })
                    }

                    // 生成子应用路由配置，采用三层嵌套结构
                    const microRoute = {
                        path: menuUrl,
                        component: 'layout/index', // 第一层：主布局
                        redirect: menuUrl,
                        meta: {
                            title: menuName,
                            breadcrumb: false,
                            code: menu.menuId,
                            icon: menu.logoUrl || '-a-8'
                        },
                        children: [
                            {
                                path: '',
                                component: 'layout/slide-layout', // 第二层：侧边菜单布局
                                name: `${microApp.name}Layout`,
                                hidden: true,
                                redirect: menuUrl,
                                meta: {
                                    hasPageChild: true,
                                    activeMenu: menuUrl,
                                    breadcrumb: false
                                },
                                children: childRoutes
                            }
                        ]
                    }
                    microAppRoutes.push(microRoute)
                }
            }

            // 递归处理子菜单
            if (menu.children && menu.children.length > 0) {
                traverseMenus(menu.children)
            }
        })
    }

    traverseMenus(menus)

    // 调试信息
    const allMicroApps = getAllDynamicMicroApps()
    const routeMapping = getDynamicRouteMapping()
    console.log('注册的微应用:', allMicroApps)
    console.log('动态路由映射:', routeMapping)

    return microAppRoutes
}

const mutations = {
    SET_ROUTES: (state, routes) => {
        state.permissionRoutes = routes
    },
    SET_PAGE_ROUTES: (state, routes) => {
        state.pageRoute = Object.assign({}, routes)
    },
    SET_MENU: (state, menu) => {
        // console.log(menu)
        state.pageMenu = menu
    },
    SORT_PAGEMENU(state, data) {
        const { list, bread = [] } = data
        // 变更菜单顺序，然后排序
        const menuMap = {}
        list.forEach((item) => {
            menuMap[item.menuId] = item.menuIndex
        })
        const { pageMenu } = state
        let levelData = []
        // 找到对应的菜单列表
        if (bread.length <= 1) {
            // 第一层菜单
            levelData = pageMenu
        } else {
            // 通过层级关系找到对应的菜单列表
            let findData = pageMenu
            for (let i = 1; i < bread.length; i++) {
                const findItem = findData.find((item) => item.menuId === bread[i].menuId)
                findItem && (levelData = findItem.children)
            }
        }
        // 有数据才更新顺序
        if (levelData && levelData.length) {
            levelData.forEach((menu) => {
                const { menuId } = menu
                const item = menuMap[menuId]
                item && (menu.menuIndex = item)
            })
            levelData.sort((a, b) => a.menuIndex - b.menuIndex)
            state.pageMenu = pageMenu
        }
    }
}

const actions = {
    // 根据权限菜单添加路由
    generateRoutes({ commit }, peload) {
        console.log(peload)
        const passPermissionRouter = peload.permissionRouter
        return new Promise(async (resolve) => {
            const { authBtns, authMenus } = peload
            const childrenUrlAddedMenu = addMenuParentPath(peload.menus)
            console.log('用户权限菜单 authMenus:', authMenus)
            console.log('原始菜单数据 peload.menus:', peload.menus)
            console.log(childrenUrlAddedMenu, 'childrenUrlAddedMenu')
            // 生成子应用路由
            const microAppRoutes = generateMicroAppRoutes(peload.menus || [])
            console.log('生成的微前端路由：', microAppRoutes)
            // 合并主应用和子应用路由
            const allPermissionRoutes = [
                ...(passPermissionRouter || permissionRouter),
                ...microAppRoutes
            ]

            const filterObj = filterRoutes(allPermissionRoutes, { authBtns, authMenus })
            console.log('过滤后的路由：', filterObj)

            // 检查微前端路由是否被过滤掉
            const microAppRoutesInFiltered = filterObj.router.filter(
                (route) =>
                    route.path &&
                    (route.path.includes('observeCenter') || route.path.includes('observation'))
            )
            console.log('过滤后的微前端路由：', microAppRoutesInFiltered)
            commit('SET_ROUTES', filterObj.router)
            if (!config.menuPermissions) {
                commit('SET_MENU', filterObj.menu)
            } else {
                commit('SET_MENU', childrenUrlAddedMenu)
            }
            resolve(filterObj)
        })
    },
    // 过滤page下的路由
    generatePageRoutes({ commit }, routes) {
        const res = filterPageRoutes(routes)
        commit('SET_PAGE_ROUTES', res)
    },
    // 排序菜单
    changePageMenuIndex({ commit }, config) {
        commit('SORT_PAGEMENU', config)
    }
}

export default {
    namespaced: true,
    state,
    mutations,
    actions
}
