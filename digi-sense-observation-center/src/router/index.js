import Vue from 'vue'
import Router from 'vue-router'
import baseRouter from './base-router'
import config from '@/config'
Vue.use(Router)

// 解决vue-router版本更新产生的问题 导致路由跳转失败抛出错误
// 由于 this.$router.push() 返回的是Promise对象, 此时then方法不能正常执行, 无法跳转到指定路由, 就触发了该对象的捕获错误的方法, throw抛出错误, 但并不影响程序功能.
const originalPush = Router.prototype.push
Router.prototype.push = function push(location, onResolve, onReject) {
    if (onResolve || onReject) {
        return originalPush.call(this, location, onResolve, onReject)
    }
    return originalPush.call(this, location).catch((err) => err)
}

/**
 * 获取路由base路径
 * @returns {string} 路由base路径
 */
const getRouterBase = () => {
    // qiankun环境下，使用子应用的路径前缀作为base
    if (window.__POWERED_BY_QIANKUN__) {
        return '/observation-center/'
    }
    // 非qiankun环境，使用原有逻辑
    return config.routeMode === 'history' ? process.env.BASE_URL : ''
}

const createRouter = () =>
    new Router({
        mode: config.routeMode,
        base: getRouterBase(),
        scrollBehavior: () => ({ y: 0 }),
        routes: [...baseRouter]
    })

const router = createRouter()

// 重置路由
export const resetRouter = () => {
    const newRouter = createRouter()
    router.matcher = newRouter.matcher
}
// 找路由
export const getRouteByName = (route, name) => {
    let findRoute = ''
    if (Array.isArray(route)) {
        for (let i = 0; i < route.length; i++) {
            if (route[i].name === name) {
                findRoute = route[i]
            } else {
                if (route[i].children && route[i].children.length) {
                    findRoute = getRouteByName(route[i].children, name)
                }
            }
            if (findRoute) {
                break
            }
        }
    } else {
        if (route.name === name) {
            findRoute = route
        }
    }
    return findRoute
}

export default router
