import Mock from 'mockjs'
const random = Mock.Random
const MockList = Mock.mock({
    USER_STATUS: [
        { name: '有效', code: '有效' },
        { name: '无效', code: '无效' },
        { name: '冻结', code: '冻结' }
    ],
    MENU_TYPE: [
        { name: '系统链接', code: '0' },
        { name: '外系统链接', code: '-1' }
    ],
    MENU__OPEN_MODE: [
        { name: '新窗口打开', code: '0' },
        { name: '当前页打开', code: '-1' }
    ],
    NOTICE_TYPES: [
        { name: '销售', code: '销售' },
        { name: '假期', code: '假期' },
        { name: '值班', code: '值班' }
    ],
    NOTICE_STATUS: [
        { name: '已发布', code: '已发布' },
        { name: '已生效', code: '已生效' },
        { name: '已作废', code: '已作废' }
    ]
})
const list = MockList
export const getDict = params => {
    return {
        code: 0,
        data: {
            dict: list[params.dictCode]
        }
    }
}

export const getGoodsPrivPage = params => {
    return {
        "code": "0",
        "data": {
            "goodsGroupPrivList": [],
            "goodsPrivList": [
                {
                    "goods": {
                        "statusDate": 1647392916000,
                        "lowestPrice": 1,
                        "updateDate": 1647392916000,
                        "goodsNbr": "7NBR",
                        "goodsId": 7,
                        "saleType": "1",
                        "remark": "",
                        "totalCount": 1,
                        "packageType": "2",
                        "expDate": 1647392916000,
                        "features": "1",
                        "score": 1,
                        "effDate": 1647392916000,
                        "storeName": "1",
                        "purcPlace": "1",
                        "packageName": "测试商品1",
                        "goodsName": "测试商品1",
                        "isPriv": 0,
                        "createDate": 1647392916000,
                        "goodsDesc": "",
                        "onSaleType": "1",
                        "highestPrice": 1,
                        "saleCount": 1,
                        "packageNbr": "7NBR",
                        "collectCount": 1,
                        "statusCd": "1000",
                        "storeId": 1,
                        "version": 1,
                        "barCode": "1",
                        "createStaff": 99,
                        "goodsType": "2",
                        "promRelaFlag": "1",
                        "onSaleTime": 1647392916000,
                        "stockReduceType": "1",
                        "goodsStatus": "1000",
                        "getType": "1",
                        "commentsCount": 1,
                        "attrJson": "",
                        "ftId": 1,
                        "paymentMethod": "1",
                        "spuId": 1,
                        "categoryId": 1,
                        "updateStaff": 99
                    },
                    "isPriv": 0
                }
            ],
            "list": [
                {
                    "statusDate": 1647392916000,
                    "lowestPrice": 1,
                    "updateDate": 1647392916000,
                    "goodsNbr": "7NBR",
                    "goodsId": 7,
                    "saleType": "1",
                    "remark": "",
                    "totalCount": 1,
                    "packageType": "2",
                    "expDate": 1647392916000,
                    "features": "1",
                    "score": 1,
                    "effDate": 1647392916000,
                    "storeName": "1",
                    "purcPlace": "1",
                    "packageName": "测试商品1",
                    "goodsName": "测试商品1",
                    "isPriv": 0,
                    "createDate": 1647392916000,
                    "goodsDesc": "",
                    "onSaleType": "1",
                    "highestPrice": 1,
                    "saleCount": 1,
                    "packageNbr": "7NBR",
                    "collectCount": 1,
                    "statusCd": "1000",
                    "storeId": 1,
                    "version": 1,
                    "barCode": "1",
                    "createStaff": 99,
                    "goodsType": "2",
                    "promRelaFlag": "1",
                    "onSaleTime": 1647392916000,
                    "stockReduceType": "1",
                    "goodsStatus": "1000",
                    "getType": "1",
                    "commentsCount": 1,
                    "attrJson": "",
                    "ftId": 1,
                    "paymentMethod": "1",
                    "spuId": 1,
                    "categoryId": 1,
                    "updateStaff": 99
                },
                {
                    "statusDate": 1647392916000,
                    "lowestPrice": 1,
                    "updateDate": 1647392916000,
                    "goodsNbr": "7NBR",
                    "goodsId": 8,
                    "saleType": "1",
                    "remark": "",
                    "totalCount": 1,
                    "packageType": "2",
                    "expDate": 1647392916000,
                    "features": "1",
                    "score": 1,
                    "effDate": 1647392916000,
                    "storeName": "1",
                    "purcPlace": "1",
                    "packageName": "测试商品1",
                    "goodsName": "测试商品1",
                    "isPriv": 0,
                    "createDate": 1647392916000,
                    "goodsDesc": "",
                    "onSaleType": "1",
                    "highestPrice": 1,
                    "saleCount": 1,
                    "packageNbr": "7NBR",
                    "collectCount": 1,
                    "statusCd": "1000",
                    "storeId": 1,
                    "version": 1,
                    "barCode": "1",
                    "createStaff": 99,
                    "goodsType": "2",
                    "promRelaFlag": "1",
                    "onSaleTime": 1647392916000,
                    "stockReduceType": "1",
                    "goodsStatus": "1000",
                    "getType": "1",
                    "commentsCount": 1,
                    "attrJson": "",
                    "ftId": 1,
                    "paymentMethod": "1",
                    "spuId": 1,
                    "categoryId": 1,
                    "updateStaff": 99
                },
            ]
        },
        "message": "OK",
        "uuid": "5a05594194424673ae6f5f93e9d809c7"
    }
}
export const saveGoodsPriv = params => {
    return {
        "code": "0",
        "data": {},
        "message": "OK",
        "uuid": "5f56d047a3004b1ba843f6716561d352"
    }
}