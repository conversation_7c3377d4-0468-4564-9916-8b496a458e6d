import Mock from 'mockjs'
const random = Mock.Random
const MockList = Mock.mock({
    USER_STATUS: [
        { name: '有效', code: '有效' },
        { name: '无效', code: '无效' },
        { name: '冻结', code: '冻结' }
    ],
    MENU_TYPE: [
        { name: '系统链接', code: '0' },
        { name: '外系统链接', code: '-1' }
    ],
    MENU__OPEN_MODE: [
        { name: '新窗口打开', code: '0' },
        { name: '当前页打开', code: '-1' }
    ],
    NOTICE_TYPES: [
        { name: '销售', code: '销售' },
        { name: '假期', code: '假期' },
        { name: '值班', code: '值班' }
    ],
    NOTICE_STATUS: [
        { name: '已发布', code: '已发布' },
        { name: '已生效', code: '已生效' },
        { name: '已作废', code: '已作废' }
    ]
})
const list = MockList
export const getDict = params => {
    return {
        code: 0,
        data: {
            dict: list[params.dictCode]
        }
    }
}

export const queryPage = params => {
    return {
        "code": "0",
        "data": {
            "beginIndex": 0,
            "endIndex": 0,
            "list": [
                {
                    "attrJson": "",
                    "barCode": "1",
                    "categoryId": 1,
                    "collectCount": 1,
                    "commentsCount": 1,
                    "createDate": "2022-03-16 09:08:36",
                    "createStaff": 99,
                    "effDate": "2022-03-16 09:08:36",
                    "expDate": "2022-03-16 09:08:36",
                    "features": "1",
                    "ftId": 1,
                    "getType": "1",
                    "goodsDesc": "",
                    "goodsId": 7,
                    "goodsName": "测试商品1",
                    "goodsNbr": "7NBR",
                    "goodsStatus": "1000",
                    "goodsType": "2",
                    "highestPrice": 1,
                    "lowestPrice": 1,
                    "onSaleTime": "2022-03-16 09:08:36",
                    "onSaleType": "1",
                    "paymentMethod": "1",
                    "promRelaFlag": "1",
                    "purcPlace": "1",
                    "remark": "",
                    "saleCount": 1,
                    "saleType": "1",
                    "score": 1,
                    "spuId": 1,
                    "statusCd": "1000",
                    "statusDate": "2022-03-16 09:08:36",
                    "stockReduceType": "1",
                    "storeId": 1,
                    "storeName": "1",
                    "totalCount": 1,
                    "updateDate": "2022-03-16 09:08:36",
                    "updateStaff": 99,
                    "version": 1
                },
                {
                    "attrJson": "0",
                    "barCode": "1",
                    "categoryId": 1,
                    "collectCount": 0,
                    "commentsCount": 0,
                    "createDate": "2022-03-15 00:00:00",
                    "createStaff": 0,
                    "effDate": "2022-01-01 00:00:00",
                    "expDate": "2023-01-01 00:00:00",
                    "features": "0",
                    "ftId": 0,
                    "getType": "0",
                    "goodsDesc": "物联网定制DNN商品",
                    "goodsId": 110,
                    "goodsName": "定制DNN",
                    "goodsNbr": "off1",
                    "goodsStatus": "1200",
                    "goodsType": "1000",
                    "highestPrice": 0,
                    "lowestPrice": 0,
                    "onSaleTime": "2022-03-15 00:00:00",
                    "onSaleType": "立即上架",
                    "paymentMethod": "0",
                    "promRelaFlag": "0",
                    "purcPlace": "0",
                    "remark": "00",
                    "saleCount": 200,
                    "saleType": "0",
                    "score": 0,
                    "spuId": 20001,
                    "statusCd": "0",
                    "statusDate": "2022-03-15 00:00:00",
                    "stockReduceType": "0",
                    "storeId": 1,
                    "storeName": "11",
                    "totalCount": 1000,
                    "updateDate": "2022-03-15 00:00:00",
                    "updateStaff": 0,
                    "version": 1
                },
                {
                    "attrJson": "0",
                    "barCode": "1",
                    "categoryId": 1,
                    "collectCount": 0,
                    "commentsCount": 0,
                    "createDate": "2022-03-15 00:00:00",
                    "createStaff": 0,
                    "effDate": "2022-01-01 00:00:00",
                    "expDate": "2023-01-01 00:00:00",
                    "features": "0",
                    "ftId": 0,
                    "getType": "0",
                    "goodsDesc": "5G乐通流量包商品",
                    "goodsId": 120,
                    "goodsName": "5G-乐通（流量）",
                    "goodsNbr": "off2",
                    "goodsStatus": "1100",
                    "goodsType": "1000",
                    "highestPrice": 0,
                    "lowestPrice": 0,
                    "onSaleTime": "2022-03-15 00:00:00",
                    "onSaleType": "0",
                    "paymentMethod": "0",
                    "promRelaFlag": "0",
                    "purcPlace": "0",
                    "remark": "00",
                    "saleCount": 0,
                    "saleType": "0",
                    "score": 0,
                    "spuId": 20001,
                    "statusCd": "0",
                    "statusDate": "2022-03-15 00:00:00",
                    "stockReduceType": "0",
                    "storeId": 1,
                    "storeName": "1",
                    "totalCount": 0,
                    "updateDate": "2022-03-15 00:00:00",
                    "updateStaff": 0,
                    "version": 1
                },
                {
                    "attrJson": "0",
                    "barCode": "1",
                    "categoryId": 1,
                    "collectCount": 0,
                    "commentsCount": 0,
                    "createDate": "2022-03-15 00:00:00",
                    "createStaff": 0,
                    "effDate": "2022-01-01 00:00:00",
                    "expDate": "2023-01-01 00:00:00",
                    "features": "0",
                    "ftId": 0,
                    "getType": "0",
                    "goodsDesc": "贴片卡商品",
                    "goodsId": 130,
                    "goodsName": "UIM卡",
                    "goodsNbr": "off3",
                    "goodsStatus": "1100",
                    "goodsType": "1000",
                    "highestPrice": 0,
                    "lowestPrice": 0,
                    "onSaleTime": "2022-03-15 00:00:00",
                    "onSaleType": "0",
                    "paymentMethod": "0",
                    "promRelaFlag": "0",
                    "purcPlace": "0",
                    "remark": "00",
                    "saleCount": 0,
                    "saleType": "0",
                    "score": 0,
                    "spuId": 20001,
                    "statusCd": "0",
                    "statusDate": "2022-03-15 00:00:00",
                    "stockReduceType": "0",
                    "storeId": 1,
                    "storeName": "1",
                    "totalCount": 0,
                    "updateDate": "2022-03-15 00:00:00",
                    "updateStaff": 0,
                    "version": 1
                }
            ],
            "pageIndex": 0,
            "pageSize": 0,
            "pages": 0,
            "total": 0
        },
        "message": "OK",
        "uuid": "737823b831fd4594af711fe76dcfbcaa"
    }
}
export const getGoodsSettingDetail = params => {
    return {
        "code": "0",
        "data": {
            "goodsAttrDtoList": [
                {
                    "attrCode": "5G-TINGXIN(SA)",
                    "attrName": "5G数据通信（SA）",
                    "attrType": "0",
                    "attrVal": "IMS功能：关闭/打开",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900001,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 1,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "PAY",
                    "attrName": "付费方式",
                    "attrType": "0",
                    "attrVal": "预付费/后付费",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900002,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 2,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "JIHUO",
                    "attrName": "产品激活方式",
                    "attrType": "0",
                    "attrVal": "首话单激活/限免激活/强制激活",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900003,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 3,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "TINGJI",
                    "attrName": "初始停机要求",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900004,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 4,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "HANGYE",
                    "attrName": "行业属性",
                    "attrType": "0",
                    "attrVal": "工业物联网/车联网",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900005,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 5,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "CUIJIAO",
                    "attrName": "是否免催缴",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900006,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 6,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "JIAOFEI",
                    "attrName": "约定缴费期",
                    "attrType": "0",
                    "attrVal": "1个月",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900007,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 7,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "JIFEI",
                    "attrName": "离在线计费模式",
                    "attrType": "0",
                    "attrVal": "离线计费",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900008,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 8,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "MIANJIE",
                    "attrName": "是否免结算",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900009,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 9,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "BEIAN",
                    "attrName": "备案客户",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900010,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 10,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-GW",
                    "attrName": "是否开通5G-公网访问控制",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900011,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 11,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-DZ",
                    "attrName": "是否开通5G定制专网DNN",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900012,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 12,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-DX",
                    "attrName": "是否开通5G-定向访问黑名单",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900013,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 13,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "JIHUO",
                    "attrName": "是否强制激活",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900014,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 14,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-JKBD",
                    "attrName": "是否开通5G机卡绑定",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900015,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 15,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-XZ",
                    "attrName": "是否开通5G区域限制",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900016,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 16,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "DLDW",
                    "attrName": "是否达量断网",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900017,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 17,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "ZIFEI",
                    "attrName": "资费档次",
                    "attrType": "0",
                    "attrVal": "5G测试卡套餐（100G、30分钟、30条、3个月）",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900018,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 18,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "SHOUFEI",
                    "attrName": "收费单位",
                    "attrType": "0",
                    "attrVal": "贴片卡/插入卡",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900019,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 19,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "SXDW",
                    "attrName": "销售单位",
                    "attrType": "0",
                    "attrVal": "填写框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900020,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 20,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "HZH",
                    "attrName": "省份合帐目标号码产品类型",
                    "attrType": "0",
                    "attrVal": "填写框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900021,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 21,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "HZHM",
                    "attrName": "省份合帐目标号码",
                    "attrType": "0",
                    "attrVal": "填写框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900022,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 22,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "WORKER",
                    "attrName": "客户经理",
                    "attrType": "0",
                    "attrVal": "填写框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900023,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 23,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "WORKER-num",
                    "attrName": "客户经理联系方式",
                    "attrType": "0",
                    "attrVal": "填写框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900024,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 24,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "ZHXX",
                    "attrName": "是否允许用户设置密码",
                    "attrType": "0",
                    "attrVal": "填写框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900025,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 25,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "100M",
                    "attrName": "100M流量限额",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900026,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 26,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "XXDW",
                    "attrName": "销售单位（区县）",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900027,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 27,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "TX",
                    "attrName": "是否免提醒",
                    "attrType": "0",
                    "attrVal": "选择框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900028,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 28,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "YUZHI",
                    "attrName": "阈值提醒（比例）",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900029,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 29,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "TX",
                    "attrName": "阈值提醒（数值，单位M）",
                    "attrType": "0",
                    "attrVal": "60、80、90",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900030,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 30,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "ZHXX",
                    "attrName": "免停生效时间",
                    "attrType": "0",
                    "attrVal": "选择框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900031,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 31,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "SXSJ",
                    "attrName": "免停失效时间",
                    "attrType": "0",
                    "attrVal": "选择时间",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900032,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 32,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "KAIKA",
                    "attrName": "一点开卡",
                    "attrType": "0",
                    "attrVal": "选择时间",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900033,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 33,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "KAIKA",
                    "attrName": "开卡市",
                    "attrType": "0",
                    "attrVal": "是/否",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900034,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 34,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "LUODISHI",
                    "attrName": "落地市",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900035,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 35,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-DNN",
                    "attrName": "5G-公网访问控制-DNN名称",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900036,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 36,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-IP",
                    "attrName": "5G-公网访问控制-IP协议栈",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900037,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 37,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-DX",
                    "attrName": "5G-定向访问黑名单-编码",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900038,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 38,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "QJIHUO",
                    "attrName": "强制激活期",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900039,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 39,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "DWLX",
                    "attrName": "达量断网类型",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900040,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 40,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "DWYZ",
                    "attrName": "达量断网阈值",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900041,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 41,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "DNN-NAME",
                    "attrName": "DNN名称",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900042,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 42,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "GNF",
                    "attrName": "物联网功能费",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900043,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 43,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "IPV6",
                    "attrName": "固定IPv6地址",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900044,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 44,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "IPV4",
                    "attrName": "固定IPv4地址",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900045,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 45,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "5G-DXFW",
                    "attrName": "5G定向访问群号码",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900046,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 46,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "C-TIME",
                    "attrName": "重绑定次数",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900047,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 47,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "JKBD-LX",
                    "attrName": "机卡绑定类型",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900048,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 48,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "IMEI",
                    "attrName": "IMEI号",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900049,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 49,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "DXLX",
                    "attrName": "定向类型",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900050,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 50,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "DXYQ",
                    "attrName": "定向园区",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900051,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 51,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "DXS",
                    "attrName": "定向省",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900052,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 52,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "attrCode": "YQ-M",
                    "attrName": "延期时间(月)",
                    "attrType": "0",
                    "attrVal": "输入框",
                    "categoryAttrId": 0,
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "features": "0",
                    "goodsAttrId": 900053,
                    "goodsId": 120,
                    "remark": "0",
                    "sort": 53,
                    "spuAttrId": 10001,
                    "statusCd": "0",
                    "statusDate": "2022-01-01 00:00:00",
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                }
            ],
            "goodsDto": {
                "attrJson": "0",
                "barCode": "1",
                "categoryId": 1,
                "collectCount": 0,
                "commentsCount": 0,
                "createDate": "2022-03-15 00:00:00",
                "createStaff": 0,
                "effDate": "2022-01-01 00:00:00",
                "expDate": "2023-01-01 00:00:00",
                "features": "0",
                "ftId": 0,
                "getType": "0",
                "goodsDesc": "5G乐通流量包商品",
                "goodsId": 120,
                "goodsName": "5G-乐通（流量）",
                "goodsNbr": "off2",
                "goodsStatus": "1100",
                "goodsType": "1000",
                "highestPrice": 0,
                "lowestPrice": 0,
                "onSaleTime": "2022-03-15 00:00:00",
                "onSaleType": "0",
                "paymentMethod": "0",
                "promRelaFlag": "0",
                "purcPlace": "0",
                "remark": "00",
                "saleCount": 0,
                "saleType": "0",
                "score": 0,
                "spuId": 20001,
                "statusCd": "0",
                "statusDate": "2022-03-15 00:00:00",
                "stockReduceType": "0",
                "storeId": 1,
                "storeName": "1",
                "totalCount": 0,
                "updateDate": "2022-03-15 00:00:00",
                "updateStaff": 0,
                "version": 1
            },
            "goodsApplAreaDtoList": [
                {
                    "regionName": "武汉",
                    "contryFlag": "1"
                }
            ]
        },
        "message": "OK",
        "uuid": "217beb0a890946689bb99d0020354393"
    }
}

export const queryGroupPage = params => {
    return {
        "code": "0",
        "data": {
            "beginIndex": 0,
            "endIndex": 0,
            "list": [
                {
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "effDate": "2022-01-01 00:00:00",
                    "expDate": "2022-02-28 00:00:00",
                    "groupDesc": "企业定制化商品组合",
                    "groupId": 11000,
                    "groupName": "湖北宜化松滋肥业-定向流量私网DNN",
                    "groupStatus": "0",
                    "groupType": "1",
                    "isMainPackage": "1000",
                    "mailedFlag": "0",
                    "preDays": 0,
                    "priceRange": "0",
                    "promFlag": "0",
                    "remark": "0",
                    "statusCd": "1000",
                    "statusDate": "2022-01-01 00:00:00",
                    "storeId": 0,
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                },
                {
                    "createDate": "2022-01-01 00:00:00",
                    "createStaff": 0,
                    "effDate": "2022-01-01 00:00:00",
                    "expDate": "2022-01-01 00:00:00",
                    "groupDesc": "企业定制化商品组合",
                    "groupId": 12000,
                    "groupName": "湖南公安总队定向短信商品",
                    "groupStatus": "0",
                    "groupType": "1",
                    "isMainPackage": "1000",
                    "mailedFlag": "0",
                    "preDays": 0,
                    "priceRange": "0",
                    "promFlag": "0",
                    "remark": "0",
                    "statusCd": "1000",
                    "statusDate": "2022-01-01 00:00:00",
                    "storeId": 0,
                    "updateDate": "2022-01-01 00:00:00",
                    "updateStaff": 0
                }
            ],
            "pageIndex": 0,
            "pageSize": 0,
            "pages": 0,
            "total": 0
        },
        "message": "OK",
        "uuid": "cb4556a8a83845b49439036604ff7330"
    }
}

export const getGoodsGroupSetDetail = params => {
    return {
        "code": "0",
        "data": {
            "goodsDtoList": [
                {
                    "attrJson": "0",
                    "barCode": "1",
                    "categoryId": 1,
                    "collectCount": 0,
                    "commentsCount": 0,
                    "createDate": "2022-03-15 00:00:00",
                    "createStaff": 0,
                    "effDate": "2022-01-01 00:00:00",
                    "expDate": "2023-01-01 00:00:00",
                    "features": "0",
                    "ftId": 0,
                    "getType": "0",
                    "goodsDesc": "联合卡",
                    "goodsId": 11,
                    "goodsName": "联合卡",
                    "goodsNbr": "off44",
                    "goodsStatus": "1000",
                    "goodsType": "1000",
                    "highestPrice": 0,
                    "lowestPrice": 0,
                    "onSaleTime": "2022-03-15 00:00:00",
                    "onSaleType": "0",
                    "paymentMethod": "0",
                    "promRelaFlag": "0",
                    "purcPlace": "0",
                    "remark": "00",
                    "saleCount": 0,
                    "saleType": "0",
                    "score": 0,
                    "spuId": 514000,
                    "statusCd": "1000",
                    "statusDate": "2022-03-15 00:00:00",
                    "stockReduceType": "0",
                    "storeId": 1,
                    "storeName": "1",
                    "totalCount": 0,
                    "updateDate": "2022-03-15 00:00:00",
                    "updateStaff": 0,
                    "version": 1
                },
                {
                    "attrJson": "0",
                    "barCode": "1",
                    "categoryId": 1,
                    "collectCount": 0,
                    "commentsCount": 0,
                    "createDate": "2022-03-15 00:00:00",
                    "createStaff": 0,
                    "effDate": "2022-01-01 00:00:00",
                    "expDate": "2023-01-01 00:00:00",
                    "features": "0",
                    "ftId": 0,
                    "getType": "0",
                    "goodsDesc": "4G升5G过度",
                    "goodsId": 12,
                    "goodsName": "4G升5G过度",
                    "goodsNbr": "off45",
                    "goodsStatus": "1000",
                    "goodsType": "1000",
                    "highestPrice": 0,
                    "lowestPrice": 0,
                    "onSaleTime": "2022-03-15 00:00:00",
                    "onSaleType": "0",
                    "paymentMethod": "0",
                    "promRelaFlag": "0",
                    "purcPlace": "0",
                    "remark": "00",
                    "saleCount": 0,
                    "saleType": "0",
                    "score": 0,
                    "spuId": 514000,
                    "statusCd": "1000",
                    "statusDate": "2022-03-15 00:00:00",
                    "stockReduceType": "0",
                    "storeId": 1,
                    "storeName": "1",
                    "totalCount": 0,
                    "updateDate": "2022-03-15 00:00:00",
                    "updateStaff": 0,
                    "version": 1
                },
                {
                    "attrJson": "0",
                    "barCode": "1",
                    "categoryId": 1,
                    "collectCount": 0,
                    "commentsCount": 0,
                    "createDate": "2022-03-15 00:00:00",
                    "createStaff": 0,
                    "effDate": "2022-01-01 00:00:00",
                    "expDate": "2023-01-01 00:00:00",
                    "features": "0",
                    "ftId": 0,
                    "getType": "0",
                    "goodsDesc": "电信宽带200M升级款",
                    "goodsId": 13,
                    "goodsName": "电信宽带200M升级款",
                    "goodsNbr": "off46",
                    "goodsStatus": "1000",
                    "goodsType": "1000",
                    "highestPrice": 0,
                    "lowestPrice": 0,
                    "onSaleTime": "2022-03-15 00:00:00",
                    "onSaleType": "0",
                    "paymentMethod": "0",
                    "promRelaFlag": "0",
                    "purcPlace": "0",
                    "remark": "00",
                    "saleCount": 0,
                    "saleType": "0",
                    "score": 0,
                    "spuId": 514000,
                    "statusCd": "1000",
                    "statusDate": "2022-03-15 00:00:00",
                    "stockReduceType": "0",
                    "storeId": 1,
                    "storeName": "1",
                    "totalCount": 0,
                    "updateDate": "2022-03-15 00:00:00",
                    "updateStaff": 0,
                    "version": 1
                }
            ],
            "goodsGroupDetailDtoList": [
                {
                    "createDate": "2022-03-18 15:08:45",
                    "createStaff": 0,
                    "goodsId": 11,
                    "groupDetailId": 4,
                    "groupDetailType": "1",
                    "groupId": 101,
                    "num": 1,
                    "parentDetailId": 0,
                    "priceRange": "1000",
                    "remark": "",
                    "skuId": 0,
                    "statusCd": "1000",
                    "statusDate": "2022-03-18 15:08:45",
                    "storeId": 1,
                    "updateDate": "2022-03-18 15:08:45",
                    "updateStaff": 0
                },
                {
                    "createDate": "2022-03-18 15:08:45",
                    "createStaff": 0,
                    "goodsId": 12,
                    "groupDetailId": 5,
                    "groupDetailType": "1",
                    "groupId": 101,
                    "num": 1,
                    "parentDetailId": 0,
                    "priceRange": "1000",
                    "remark": "",
                    "skuId": 0,
                    "statusCd": "1000",
                    "statusDate": "2022-03-18 15:08:45",
                    "storeId": 1,
                    "updateDate": "2022-03-18 15:08:45",
                    "updateStaff": 0
                },
                {
                    "createDate": "2022-03-18 15:08:45",
                    "createStaff": 0,
                    "goodsId": 13,
                    "groupDetailId": 6,
                    "groupDetailType": "1",
                    "groupId": 101,
                    "num": 1,
                    "parentDetailId": 0,
                    "priceRange": "1000",
                    "remark": "",
                    "skuId": 0,
                    "statusCd": "1000",
                    "statusDate": "2022-03-18 15:08:45",
                    "storeId": 1,
                    "updateDate": "2022-03-18 15:08:45",
                    "updateStaff": 0
                }
            ],
            "goodsGroupDto": {
                "createDate": "2022-01-01 00:00:00",
                "createStaff": 0,
                "effDate": "2022-01-01 00:00:00",
                "expDate": "2022-01-01 00:00:00",
                "groupDesc": "企业定制化商品组合",
                "groupId": 101,
                "groupName": "湖百公安总队定向短信商品",
                "groupStatus": "0",
                "groupType": "1",
                "isMainPackage": "1000",
                "mailedFlag": "0",
                "preDays": 0,
                "priceRange": "1000",
                "promFlag": "0",
                "remark": "0",
                "statusCd": "1000",
                "statusDate": "2022-01-01 00:00:00",
                "storeId": 0,
                "updateDate": "2022-01-01 00:00:00",
                "updateStaff": 0
            }
        },
        "message": "OK",
        "uuid": "fdedf5493ad54fae8f9c3f2fd71d8a1b"
    }
}